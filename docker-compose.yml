version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: teltonika_postgres
    environment:
      POSTGRES_DB: teltonika_gps
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./src/database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: teltonika_app
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: teltonika_gps
      DB_USER: postgres
      DB_PASSWORD: postgres123
      JWT_SECRET: your_super_secret_jwt_key_here_change_in_production
      NODE_ENV: production
    ports:
      - "3000:3000"
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

volumes:
  postgres_data:
