# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=teltonika_gps
DB_USER=postgres
DB_PASSWORD=your_password

# Server Configuration
PORT=3000
TCP_PORT=8080

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# Environment
NODE_ENV=development

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
