# Teltonika GPS Tracking System

A comprehensive GPS tracking system for managing Teltonika satellite devices like the FMB920. This system provides real-time GPS tracking, device management, and user authentication with a web-based interface.

## Features

- **TCP Server**: Handles connections from Teltonika devices (FMB920, etc.)
- **Message Parsing**: Decodes Teltonika protocol messages (Codec8, Codec8 Extended, Codec7)
- **Database Storage**: PostgreSQL database for GPS data, vehicles, and users
- **REST API**: Complete API for data access and management
- **Web Interface**: Real-time dashboard with interactive maps
- **User Management**: Authentication and role-based access control
- **Real-time Tracking**: Live vehicle positions and status updates

## Architecture

### Database Schema
- **GPS Data Table**: Stores location, speed, direction, altitude, ignition status, odometer
- **Vehicle Table**: Device descriptions and metadata
- **Users Table**: User authentication and account types (normal/admin)
- **User-Vehicle Associations**: Many-to-many relationship for access control

### Components
- **TCP Server**: Listens on port 8080 for device connections
- **HTTP Server**: REST API and web interface on port 3000
- **Message Parser**: Uses `complete-teltonika-parser` library
- **Authentication**: JWT-based with bcrypt password hashing

## Prerequisites

- Node.js 16.0.0 or higher
- PostgreSQL 12 or higher
- npm or yarn package manager

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd teltonika-gps-tracker
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=teltonika_gps
   DB_USER=postgres
   DB_PASSWORD=your_password
   JWT_SECRET=your_super_secret_jwt_key_here
   ```

4. **Create PostgreSQL database**
   ```sql
   CREATE DATABASE teltonika_gps;
   ```

5. **Run database migrations**
   ```bash
   npm run db:migrate
   ```

6. **Start the application**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## Usage

### Web Interface

1. Open your browser and navigate to `http://localhost:3000`
2. Create an admin user (see API section below)
3. Login with your credentials
4. View real-time vehicle positions on the map
5. Monitor vehicle status and GPS data

### Device Configuration

Configure your Teltonika device to send data to your server:

1. **Server Settings**:
   - Server IP: Your server's IP address
   - Server Port: 8080 (TCP_PORT in .env)
   - Protocol: TCP

2. **Data Sending**:
   - Enable GPS data sending
   - Set appropriate sending intervals
   - Configure IO elements as needed

### API Endpoints

#### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/verify` - Verify JWT token

#### GPS Data
- `GET /api/gps/data` - Get GPS data with pagination
- `GET /api/gps/latest/:imei` - Get latest position
- `GET /api/gps/vehicles` - Get all vehicles with positions
- `GET /api/gps/track/:imei` - Get GPS track/route

#### Example API Usage

**Create Admin User**:
```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123",
    "account_type": "admin"
  }'
```

**Login**:
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

**Get Vehicles** (with token):
```bash
curl -X GET http://localhost:3000/api/gps/vehicles \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | PostgreSQL host | localhost |
| `DB_PORT` | PostgreSQL port | 5432 |
| `DB_NAME` | Database name | teltonika_gps |
| `DB_USER` | Database user | postgres |
| `DB_PASSWORD` | Database password | - |
| `PORT` | HTTP server port | 3000 |
| `TCP_PORT` | TCP server port for devices | 8080 |
| `JWT_SECRET` | JWT signing secret | - |
| `JWT_EXPIRES_IN` | JWT expiration time | 24h |
| `NODE_ENV` | Environment | development |
| `LOG_LEVEL` | Logging level | info |

### Teltonika Protocol Support

This system supports the following Teltonika protocols:
- **Codec8**: Main protocol for GPS data
- **Codec8 Extended**: Extended version with additional data
- **Codec7**: Legacy protocol support

Supported IO Elements:
- **239**: Ignition status
- **199**: Odometer reading
- **Custom elements**: Stored in variable_data JSON field

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt with salt rounds
- **Rate Limiting**: API request throttling
- **CORS Protection**: Configurable cross-origin requests
- **Helmet Security**: Security headers and protections
- **Role-based Access**: Admin and normal user roles
- **Vehicle Access Control**: Users can only access assigned vehicles

## Monitoring and Logging

- **Winston Logging**: Structured logging to files and console
- **Error Handling**: Comprehensive error catching and reporting
- **Health Check**: `/api/health` endpoint for monitoring
- **Connection Tracking**: TCP connection monitoring and management

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify database credentials in `.env`
   - Ensure database exists

2. **Device Not Connecting**
   - Check TCP port (8080) is open
   - Verify device configuration
   - Check server logs for connection attempts

3. **Authentication Issues**
   - Verify JWT_SECRET is set
   - Check token expiration
   - Ensure user exists in database

### Logs

Check application logs in the `logs/` directory:
- `combined.log`: All log messages
- `error.log`: Error messages only

## Development

### Project Structure
```
src/
├── config/         # Database and configuration
├── database/       # Schema and migrations
├── middleware/     # Authentication and validation
├── routes/         # API endpoints
├── services/       # Business logic
├── tcp/           # TCP server for devices
├── utils/         # Utilities and helpers
└── server.js      # Main application entry
```

### Running Tests
```bash
npm test
```

### Database Operations
```bash
# Run migrations
npm run db:migrate

# Seed database (if implemented)
npm run db:seed
```

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review application logs
3. Create an issue in the repository
