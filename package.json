{"name": "teltonika-gps-tracker", "version": "1.0.0", "description": "GPS tracking system for Teltonika satellite devices like FMB920", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js"}, "keywords": ["telton<PERSON>", "gps", "tracking", "fmb920", "tcp", "postgresql"], "author": "Your Name", "license": "MIT", "dependencies": {"complete-teltonika-parser": "^0.3.6", "express": "^4.18.2", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}