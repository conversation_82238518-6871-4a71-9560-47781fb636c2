const pool = require('../config/database');
const logger = require('../utils/logger');

/**
 * Save GPS data to database
 * @param {Object} gpsData - GPS data object
 */
async function saveGpsData(gpsData) {
  const client = await pool.connect();
  
  try {
    const query = `
      INSERT INTO gps_data (
        imei, latitude, longitude, device_timestamp, speed, 
        direction, altitude, ignition_status, odometer, 
        variable_data, priority, satellites
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING id;
    `;
    
    const values = [
      gpsData.imei,
      gpsData.latitude,
      gpsData.longitude,
      gpsData.device_timestamp,
      gpsData.speed || 0,
      gpsData.direction || 0,
      gpsData.altitude || 0,
      gpsData.ignition_status || false,
      gpsData.odometer || 0,
      gpsData.variable_data ? JSON.stringify(gpsData.variable_data) : null,
      gpsData.priority || 0,
      gpsData.satellites || 0
    ];
    
    const result = await client.query(query, values);
    logger.debug(`GPS data saved with ID: ${result.rows[0].id}`);
    
    return result.rows[0].id;
    
  } catch (error) {
    logger.error('Error saving GPS data:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get or create vehicle by IMEI
 * @param {string} imei - Device IMEI
 */
async function getOrCreateVehicle(imei) {
  const client = await pool.connect();
  
  try {
    // First, try to get existing vehicle
    let query = 'SELECT * FROM vehicles WHERE imei = $1';
    let result = await client.query(query, [imei]);
    
    if (result.rows.length > 0) {
      return result.rows[0];
    }
    
    // Create new vehicle if not exists
    query = `
      INSERT INTO vehicles (imei, name, description) 
      VALUES ($1, $2, $3) 
      RETURNING *;
    `;
    
    const values = [
      imei,
      `Vehicle ${imei}`, // Default name
      `Auto-created for IMEI ${imei}`
    ];
    
    result = await client.query(query, values);
    logger.info(`Created new vehicle for IMEI: ${imei}`);
    
    return result.rows[0];
    
  } catch (error) {
    logger.error('Error getting/creating vehicle:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get GPS data for a specific IMEI with pagination
 * @param {string} imei - Device IMEI
 * @param {Object} options - Query options
 */
async function getGpsData(imei, options = {}) {
  const client = await pool.connect();
  
  try {
    const {
      limit = 100,
      offset = 0,
      startDate,
      endDate,
      orderBy = 'device_timestamp',
      order = 'DESC'
    } = options;
    
    let query = `
      SELECT 
        id, imei, latitude, longitude, device_timestamp, 
        server_timestamp, speed, direction, altitude, 
        ignition_status, odometer, variable_data, 
        priority, satellites, created_at
      FROM gps_data 
      WHERE imei = $1
    `;
    
    const values = [imei];
    let paramCount = 1;
    
    // Add date filters if provided
    if (startDate) {
      paramCount++;
      query += ` AND device_timestamp >= $${paramCount}`;
      values.push(startDate);
    }
    
    if (endDate) {
      paramCount++;
      query += ` AND device_timestamp <= $${paramCount}`;
      values.push(endDate);
    }
    
    query += ` ORDER BY ${orderBy} ${order}`;
    query += ` LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`;
    values.push(limit, offset);
    
    const result = await client.query(query, values);
    
    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) FROM gps_data WHERE imei = $1';
    const countValues = [imei];
    let countParamCount = 1;
    
    if (startDate) {
      countParamCount++;
      countQuery += ` AND device_timestamp >= $${countParamCount}`;
      countValues.push(startDate);
    }
    
    if (endDate) {
      countParamCount++;
      countQuery += ` AND device_timestamp <= $${countParamCount}`;
      countValues.push(endDate);
    }
    
    const countResult = await client.query(countQuery, countValues);
    const totalCount = parseInt(countResult.rows[0].count);
    
    return {
      data: result.rows,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    };
    
  } catch (error) {
    logger.error('Error getting GPS data:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get latest GPS position for an IMEI
 * @param {string} imei - Device IMEI
 */
async function getLatestPosition(imei) {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT 
        id, imei, latitude, longitude, device_timestamp, 
        speed, direction, altitude, ignition_status, 
        odometer, variable_data, satellites
      FROM gps_data 
      WHERE imei = $1 
      ORDER BY device_timestamp DESC 
      LIMIT 1;
    `;
    
    const result = await client.query(query, [imei]);
    return result.rows[0] || null;
    
  } catch (error) {
    logger.error('Error getting latest position:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get all vehicles with their latest positions
 */
async function getAllVehiclesWithPositions() {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT 
        v.id, v.imei, v.name, v.description,
        g.latitude, g.longitude, g.device_timestamp,
        g.speed, g.direction, g.ignition_status,
        g.odometer, g.satellites
      FROM vehicles v
      LEFT JOIN LATERAL (
        SELECT latitude, longitude, device_timestamp, speed, 
               direction, ignition_status, odometer, satellites
        FROM gps_data 
        WHERE imei = v.imei 
        ORDER BY device_timestamp DESC 
        LIMIT 1
      ) g ON true
      ORDER BY v.name;
    `;
    
    const result = await client.query(query);
    return result.rows;
    
  } catch (error) {
    logger.error('Error getting vehicles with positions:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = {
  saveGpsData,
  getOrCreateVehicle,
  getGpsData,
  getLatestPosition,
  getAllVehiclesWithPositions
};
