<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teltonika GPS Tracker</title>
    <link href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .login-form {
            max-width: 400px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .dashboard {
            display: none;
        }

        .dashboard.active {
            display: block;
        }

        .vehicles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .vehicle-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .vehicle-card h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .vehicle-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .vehicle-info .label {
            font-weight: 500;
            color: #666;
        }

        .status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status.online {
            background: #d4edda;
            color: #155724;
        }

        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }

        .map-container {
            height: 500px;
            margin: 2rem 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛰️ Teltonika GPS Tracker</h1>
        <div class="user-info">
            <span id="username"></span>
            <button class="btn btn-danger" onclick="logout()" style="display: none;" id="logoutBtn">Logout</button>
        </div>
    </div>

    <!-- Login Form -->
    <div id="loginSection">
        <div class="login-form">
            <h2 style="text-align: center; margin-bottom: 2rem;">Login</h2>
            <div id="loginError" class="error" style="display: none;"></div>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="loginUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="loginPassword" name="password" required>
                </div>
                <button type="submit" class="btn" style="width: 100%;">Login</button>
            </form>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboard" class="dashboard">
        <div class="container">
            <div id="dashboardError" class="error" style="display: none;"></div>
            <div id="dashboardSuccess" class="success" style="display: none;"></div>
            
            <h2>Vehicle Fleet</h2>
            <div id="vehiclesContainer" class="loading">Loading vehicles...</div>
            
            <h2>Live Map</h2>
            <div id="map" class="map-container"></div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        let map;
        let vehicleMarkers = {};
        let token = localStorage.getItem('token');

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            if (token) {
                verifyToken();
            } else {
                showLogin();
            }
        });

        // Show login form
        function showLogin() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('dashboard').classList.remove('active');
            document.getElementById('logoutBtn').style.display = 'none';
        }

        // Show dashboard
        function showDashboard() {
            document.getElementById('loginSection').style.display = 'none';
            document.getElementById('dashboard').classList.add('active');
            document.getElementById('logoutBtn').style.display = 'block';
            initializeMap();
            loadVehicles();
        }

        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    document.getElementById('username').textContent = data.data.user.username;
                    showDashboard();
                } else {
                    showError('loginError', data.message);
                }
            } catch (error) {
                showError('loginError', 'Login failed. Please try again.');
            }
        });

        // Verify token
        async function verifyToken() {
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('username').textContent = data.data.user.username;
                    showDashboard();
                } else {
                    localStorage.removeItem('token');
                    token = null;
                    showLogin();
                }
            } catch (error) {
                localStorage.removeItem('token');
                token = null;
                showLogin();
            }
        }

        // Logout
        function logout() {
            localStorage.removeItem('token');
            token = null;
            showLogin();
        }

        // Initialize map
        function initializeMap() {
            if (map) return;
            
            map = L.map('map').setView([54.6872, 25.2797], 7); // Lithuania center
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
        }

        // Load vehicles
        async function loadVehicles() {
            try {
                const response = await fetch('/api/gps/vehicles', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayVehicles(data.data);
                    updateMap(data.data);
                } else {
                    showError('dashboardError', data.message);
                }
            } catch (error) {
                showError('dashboardError', 'Failed to load vehicles.');
            }
        }

        // Display vehicles in grid
        function displayVehicles(vehicles) {
            const container = document.getElementById('vehiclesContainer');
            
            if (vehicles.length === 0) {
                container.innerHTML = '<p>No vehicles found.</p>';
                return;
            }
            
            container.innerHTML = '';
            container.className = 'vehicles-grid';
            
            vehicles.forEach(vehicle => {
                const card = document.createElement('div');
                card.className = 'vehicle-card';
                
                const lastUpdate = vehicle.device_timestamp 
                    ? new Date(vehicle.device_timestamp).toLocaleString()
                    : 'Never';
                
                const isOnline = vehicle.device_timestamp && 
                    (new Date() - new Date(vehicle.device_timestamp)) < 300000; // 5 minutes
                
                card.innerHTML = `
                    <h3>${vehicle.name || `Vehicle ${vehicle.imei}`}</h3>
                    <div class="vehicle-info">
                        <span class="label">IMEI:</span>
                        <span>${vehicle.imei}</span>
                        
                        <span class="label">Status:</span>
                        <span class="status ${isOnline ? 'online' : 'offline'}">
                            ${isOnline ? 'Online' : 'Offline'}
                        </span>
                        
                        <span class="label">Last Update:</span>
                        <span>${lastUpdate}</span>
                        
                        <span class="label">Speed:</span>
                        <span>${vehicle.speed || 0} km/h</span>
                        
                        <span class="label">Ignition:</span>
                        <span>${vehicle.ignition_status ? 'ON' : 'OFF'}</span>
                        
                        <span class="label">Odometer:</span>
                        <span>${vehicle.odometer || 0} km</span>
                    </div>
                `;
                
                container.appendChild(card);
            });
        }

        // Update map with vehicle positions
        function updateMap(vehicles) {
            // Clear existing markers
            Object.values(vehicleMarkers).forEach(marker => {
                map.removeLayer(marker);
            });
            vehicleMarkers = {};
            
            const bounds = [];
            
            vehicles.forEach(vehicle => {
                if (vehicle.latitude && vehicle.longitude) {
                    const lat = parseFloat(vehicle.latitude);
                    const lng = parseFloat(vehicle.longitude);
                    
                    const isOnline = vehicle.device_timestamp && 
                        (new Date() - new Date(vehicle.device_timestamp)) < 300000;
                    
                    const icon = L.divIcon({
                        html: `<div style="background: ${isOnline ? '#28a745' : '#dc3545'}; 
                                     width: 12px; height: 12px; border-radius: 50%; 
                                     border: 2px solid white; box-shadow: 0 0 4px rgba(0,0,0,0.3);"></div>`,
                        iconSize: [16, 16],
                        className: 'vehicle-marker'
                    });
                    
                    const marker = L.marker([lat, lng], { icon })
                        .bindPopup(`
                            <strong>${vehicle.name || `Vehicle ${vehicle.imei}`}</strong><br>
                            IMEI: ${vehicle.imei}<br>
                            Speed: ${vehicle.speed || 0} km/h<br>
                            Direction: ${vehicle.direction || 0}°<br>
                            Last Update: ${vehicle.device_timestamp ? 
                                new Date(vehicle.device_timestamp).toLocaleString() : 'Never'}
                        `);
                    
                    marker.addTo(map);
                    vehicleMarkers[vehicle.imei] = marker;
                    bounds.push([lat, lng]);
                }
            });
            
            // Fit map to show all vehicles
            if (bounds.length > 0) {
                map.fitBounds(bounds, { padding: [20, 20] });
            }
        }

        // Show error message
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            setTimeout(() => {
                errorElement.style.display = 'none';
            }, 5000);
        }

        // Auto-refresh vehicles every 30 seconds
        setInterval(() => {
            if (token && document.getElementById('dashboard').classList.contains('active')) {
                loadVehicles();
            }
        }, 30000);
    </script>
</body>
</html>
