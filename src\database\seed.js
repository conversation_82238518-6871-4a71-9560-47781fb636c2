const pool = require('../config/database');
const { createUser, associateUserWithVehicle } = require('../services/userService');
const logger = require('../utils/logger');

async function seedDatabase() {
  try {
    console.log('Seeding database...');
    
    // Create admin user
    try {
      const adminUser = await createUser({
        username: 'admin',
        password: 'admin123',
        account_type: 'admin'
      });
      console.log('✓ Admin user created:', adminUser.username);
    } catch (error) {
      if (error.message === 'Username already exists') {
        console.log('✓ Admin user already exists');
      } else {
        throw error;
      }
    }
    
    // Create demo user
    try {
      const demoUser = await createUser({
        username: 'demo',
        password: 'demo123',
        account_type: 'normal'
      });
      console.log('✓ Demo user created:', demoUser.username);
    } catch (error) {
      if (error.message === 'Username already exists') {
        console.log('✓ Demo user already exists');
      } else {
        throw error;
      }
    }
    
    // Create sample vehicles
    const sampleVehicles = [
      {
        imei: '***************',
        name: 'Fleet Vehicle 001',
        description: 'Company delivery truck'
      },
      {
        imei: '***************',
        name: 'Fleet Vehicle 002',
        description: 'Service van'
      },
      {
        imei: '***************',
        name: 'Fleet Vehicle 003',
        description: 'Executive car'
      }
    ];
    
    const client = await pool.connect();
    
    try {
      for (const vehicle of sampleVehicles) {
        // Check if vehicle exists
        const existingVehicle = await client.query(
          'SELECT id FROM vehicles WHERE imei = $1',
          [vehicle.imei]
        );
        
        if (existingVehicle.rows.length === 0) {
          // Create vehicle
          await client.query(
            'INSERT INTO vehicles (imei, name, description) VALUES ($1, $2, $3)',
            [vehicle.imei, vehicle.name, vehicle.description]
          );
          console.log(`✓ Vehicle created: ${vehicle.name} (${vehicle.imei})`);
        } else {
          console.log(`✓ Vehicle already exists: ${vehicle.name} (${vehicle.imei})`);
        }
      }
      
      // Associate demo user with first two vehicles
      try {
        const demoUserResult = await client.query(
          'SELECT id FROM users WHERE username = $1',
          ['demo']
        );
        
        if (demoUserResult.rows.length > 0) {
          const demoUserId = demoUserResult.rows[0].id;
          
          await associateUserWithVehicle(demoUserId, '***************');
          await associateUserWithVehicle(demoUserId, '***************');
          
          console.log('✓ Demo user associated with vehicles');
        }
      } catch (error) {
        console.log('Note: Could not associate demo user with vehicles:', error.message);
      }
      
      // Insert sample GPS data
      const sampleGpsData = [
        {
          imei: '***************',
          latitude: 54.6872,
          longitude: 25.2797,
          speed: 45,
          direction: 90,
          altitude: 150,
          ignition_status: true,
          odometer: 125000
        },
        {
          imei: '***************',
          latitude: 54.6900,
          longitude: 25.2800,
          speed: 0,
          direction: 180,
          altitude: 145,
          ignition_status: false,
          odometer: 89000
        },
        {
          imei: '***************',
          latitude: 54.6850,
          longitude: 25.2750,
          speed: 60,
          direction: 270,
          altitude: 155,
          ignition_status: true,
          odometer: 67000
        }
      ];
      
      for (const gpsData of sampleGpsData) {
        // Check if GPS data exists for this IMEI
        const existingData = await client.query(
          'SELECT id FROM gps_data WHERE imei = $1 LIMIT 1',
          [gpsData.imei]
        );
        
        if (existingData.rows.length === 0) {
          await client.query(`
            INSERT INTO gps_data (
              imei, latitude, longitude, device_timestamp, speed, 
              direction, altitude, ignition_status, odometer, priority, satellites
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
          `, [
            gpsData.imei,
            gpsData.latitude,
            gpsData.longitude,
            new Date(),
            gpsData.speed,
            gpsData.direction,
            gpsData.altitude,
            gpsData.ignition_status,
            gpsData.odometer,
            1,
            8
          ]);
          console.log(`✓ Sample GPS data created for IMEI: ${gpsData.imei}`);
        } else {
          console.log(`✓ GPS data already exists for IMEI: ${gpsData.imei}`);
        }
      }
      
    } finally {
      client.release();
    }
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\nDefault credentials:');
    console.log('Admin: username=admin, password=admin123');
    console.log('Demo:  username=demo, password=demo123');
    console.log('\nSample vehicles created with IMEIs:');
    sampleVehicles.forEach(v => console.log(`- ${v.name}: ${v.imei}`));
    
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
