const pool = require('../config/database');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * Create a new user
 * @param {Object} userData - User data
 */
async function createUser(userData) {
  const client = await pool.connect();
  
  try {
    const { username, password, account_type = 'normal' } = userData;
    
    // Check if user already exists
    const existingUser = await client.query(
      'SELECT id FROM users WHERE username = $1',
      [username]
    );
    
    if (existingUser.rows.length > 0) {
      throw new Error('Username already exists');
    }
    
    // Hash password
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    // Create user
    const query = `
      INSERT INTO users (username, password_hash, account_type)
      VALUES ($1, $2, $3)
      RETURNING id, username, account_type, created_at;
    `;
    
    const result = await client.query(query, [username, password_hash, account_type]);
    
    logger.info(`User created: ${username}`);
    return result.rows[0];
    
  } catch (error) {
    logger.error('Error creating user:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Authenticate user and return JWT token
 * @param {string} username 
 * @param {string} password 
 */
async function authenticateUser(username, password) {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT id, username, password_hash, account_type
      FROM users 
      WHERE username = $1;
    `;
    
    const result = await client.query(query, [username]);
    
    if (result.rows.length === 0) {
      throw new Error('Invalid credentials');
    }
    
    const user = result.rows[0];
    
    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username,
        account_type: user.account_type
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );
    
    logger.info(`User authenticated: ${username}`);
    
    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        account_type: user.account_type
      }
    };
    
  } catch (error) {
    logger.error('Error authenticating user:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get user by ID
 * @param {number} userId 
 */
async function getUserById(userId) {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT id, username, account_type, created_at, updated_at
      FROM users 
      WHERE id = $1;
    `;
    
    const result = await client.query(query, [userId]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return result.rows[0];
    
  } catch (error) {
    logger.error('Error getting user by ID:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get user's associated vehicles
 * @param {number} userId 
 */
async function getUserVehicles(userId) {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT v.id, v.imei, v.name, v.description, v.created_at
      FROM vehicles v
      INNER JOIN user_vehicles uv ON v.id = uv.vehicle_id
      WHERE uv.user_id = $1
      ORDER BY v.name;
    `;
    
    const result = await client.query(query, [userId]);
    return result.rows;
    
  } catch (error) {
    logger.error('Error getting user vehicles:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Associate user with vehicle
 * @param {number} userId 
 * @param {string} imei 
 */
async function associateUserWithVehicle(userId, imei) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    // Get or create vehicle
    let vehicleQuery = 'SELECT id FROM vehicles WHERE imei = $1';
    let vehicleResult = await client.query(vehicleQuery, [imei]);
    
    let vehicleId;
    if (vehicleResult.rows.length === 0) {
      // Create vehicle
      vehicleQuery = `
        INSERT INTO vehicles (imei, name, description)
        VALUES ($1, $2, $3)
        RETURNING id;
      `;
      vehicleResult = await client.query(vehicleQuery, [
        imei,
        `Vehicle ${imei}`,
        `Auto-created for IMEI ${imei}`
      ]);
    }
    
    vehicleId = vehicleResult.rows[0].id;
    
    // Associate user with vehicle
    const associationQuery = `
      INSERT INTO user_vehicles (user_id, vehicle_id)
      VALUES ($1, $2)
      ON CONFLICT (user_id, vehicle_id) DO NOTHING;
    `;
    
    await client.query(associationQuery, [userId, vehicleId]);
    await client.query('COMMIT');
    
    logger.info(`User ${userId} associated with vehicle ${imei}`);
    
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error associating user with vehicle:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Remove user-vehicle association
 * @param {number} userId 
 * @param {string} imei 
 */
async function removeUserVehicleAssociation(userId, imei) {
  const client = await pool.connect();
  
  try {
    const query = `
      DELETE FROM user_vehicles 
      WHERE user_id = $1 
      AND vehicle_id = (SELECT id FROM vehicles WHERE imei = $2);
    `;
    
    await client.query(query, [userId, imei]);
    logger.info(`Removed association between user ${userId} and vehicle ${imei}`);
    
  } catch (error) {
    logger.error('Error removing user-vehicle association:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Check if user has access to a specific vehicle
 * @param {number} userId 
 * @param {string} imei 
 */
async function hasVehicleAccess(userId, imei) {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT 1 FROM user_vehicles uv
      INNER JOIN vehicles v ON uv.vehicle_id = v.id
      WHERE uv.user_id = $1 AND v.imei = $2;
    `;
    
    const result = await client.query(query, [userId, imei]);
    return result.rows.length > 0;
    
  } catch (error) {
    logger.error('Error checking vehicle access:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get all users (admin only)
 */
async function getAllUsers() {
  const client = await pool.connect();
  
  try {
    const query = `
      SELECT id, username, account_type, created_at, updated_at
      FROM users 
      ORDER BY username;
    `;
    
    const result = await client.query(query);
    return result.rows;
    
  } catch (error) {
    logger.error('Error getting all users:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = {
  createUser,
  authenticateUser,
  getUserById,
  getUserVehicles,
  associateUserWithVehicle,
  removeUserVehicleAssociation,
  hasVehicleAccess,
  getAllUsers
};
